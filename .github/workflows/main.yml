name: CI/CD

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: fastapi-app-repo
          IMAGE_TAG: latest
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1

      - name: Clean local Terraform state and config
        run: |
          rm -rf terraform/.terraform
          rm -f terraform/terraform.tfstate
          rm -f terraform/.terraform.lock.hcl

      - name: Terraform Init
        run: terraform -chdir=terraform init -input=false

      - name: Terraform Apply
        run: terraform -chdir=terraform apply -auto-approve

      - name: Force new ECS deployment
        run: aws ecs update-service --cluster main-cluster --service main-service --force-new-deployment
