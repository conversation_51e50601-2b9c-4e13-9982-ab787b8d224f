name: CI/CD

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: fastapi-app-repo
          IMAGE_TAG: latest
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1

      - name: Clean local Terraform state and config
        run: |
          rm -rf terraform/.terraform
          rm -f terraform/terraform.tfstate
          rm -f terraform/.terraform.lock.hcl

      - name: Terraform Init
        run: terraform -chdir=terraform init -input=false

      - name: Force unlock Terraform state (if locked)
        run: |
          # Try to get the lock info and extract the lock ID
          LOCK_ID=$(terraform -chdir=terraform plan 2>&1 | grep -oP 'ID:\s+\K[a-f0-9-]+' | head -1 || echo "")
          if [ ! -z "$LOCK_ID" ]; then
            echo "Found lock ID: $LOCK_ID, attempting to unlock..."
            terraform -chdir=terraform force-unlock -force "$LOCK_ID" || true
          else
            echo "No lock detected or unable to extract lock ID"
          fi
        continue-on-error: true

      - name: Terraform Apply
        run: timeout 600 terraform -chdir=terraform apply -auto-approve

      - name: Force new ECS deployment
        run: aws ecs update-service --cluster main-cluster --service main-service --force-new-deployment

      - name: Cleanup Terraform state lock (if any)
        if: always()
        run: |
          # Attempt to unlock any remaining locks
          LOCK_ID=$(terraform -chdir=terraform plan 2>&1 | grep -oP 'ID:\s+\K[a-f0-9-]+' | head -1 || echo "")
          if [ ! -z "$LOCK_ID" ]; then
            echo "Cleaning up lock ID: $LOCK_ID"
            terraform -chdir=terraform force-unlock -force "$LOCK_ID" || true
          fi
        continue-on-error: true
